<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signal Framework</title>
</head>
<body>
    
<div id="app"></div>

<template>
    <div @store="{ count: 0 }">
        <h1>{count}</h1>
        <button onclick="increment()">Increment</button>
    </div>

    <div @users="{ users: ['jhon' , '<PERSON><PERSON>' , '<PERSON><PERSON>' , '<PERSON><PERSON><PERSON>' ] }">
        {#each users as user}
            <p>{user}</p>
        {/each}
    </div>

    <div @data="{ users: ['Jhon', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'], title: 'User List' }">
    <h3>{title}</h3>
    {#each users as user}
        <p>{user}</p>
    {/each}
    </div>
   
    <div @store="{isOpen: false  , isPending: true }">
        <button onclick="toggle()">Toggle</button>
        {#if isOpen}
            <p>Content is visible</p>
            {#else if isPending}
            <p>Loading...</p>
            {#else}
            <p>Content is hidden</p>
        {/if}
    </div>

    <div @store="{htmlContent : `<div>Hello World</div>`}">
         {@html htmlContent}
    </div>


    {new Date().toLocaleTimeString()}


<div @store="{count: 5, users: ['John', 'Jane']}">
        <!-- Standalone expressions (auto-wrapped in span) -->
        {new Date().toLocaleTimeString()}
        {count * 2}
        {users.length > 0 ? 'Has users' : 'No users'}
        
        <!-- Inside tags -->
        <p>Current time: {new Date().toLocaleTimeString()}</p>
        <h1>Double count: {count * 2}</h1>
        <div>Math result: {Math.max(10, count)}</div>
        <span>Array join: {users.join(', ')}</span>
</div>

</template>

<script type="module">
import { createApp } from './app.js';

// Usage
const app = createApp({
    methods: {
        increment() {
            this.count++;
        },
        toggle() {
            this.isOpen = !this.isOpen;
        }
    },
    effects() {
       setInterval(() => {
            this.count++;
        }, 1000);
    }
});

app.mount('#app');
</script>
</body>
</html>