// Signal implementation
let currentEffect = null;

function signal(value) {
    const subscribers = new Set();
    
    return {
        get value() {
            if (currentEffect) subscribers.add(currentEffect);
            return value;
        },
        set value(newValue) {
            if (value !== newValue) {
                value = newValue;
                subscribers.forEach(fn => fn());
            }
        }
    };
}

function effect(fn) {
    const execute = () => {
        currentEffect = execute;
        try {
            fn();
        } finally {
            currentEffect = null;
        }
    };
    execute();
}

// Main createApp function
function createApp(config) {
    let state = {};
    let methods = {};
    let mountTarget = null;
    
    return {
        mount(selector) {
            mountTarget = document.querySelector(selector);
            
            // Get template
            const templateEl = document.querySelector('template');
            const content = templateEl.content.cloneNode(true);
            
            // Parse all store attributes (@store, @users, etc.)
            const storeElements = content.querySelectorAll('[class*="@"], [id*="@"]');
            content.querySelectorAll('*').forEach(el => {
                Array.from(el.attributes).forEach(attr => {
                    if (attr.name.startsWith('@')) {
                        const storeData = new Function('return ' + attr.value)();
                        Object.keys(storeData).forEach(key => {
                            state[key] = signal(storeData[key]);
                        });
                        el.removeAttribute(attr.name);
                    }
                });
            });
            
            // Process {#each} loops
            const processEachLoops = (element) => {
                const walker = document.createTreeWalker(
                    element,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    if (node.textContent.includes('{#each')) {
                        textNodes.push(node);
                    }
                }
                
                textNodes.forEach(textNode => {
                    const parent = textNode.parentElement;
                    const content = parent.innerHTML;
                    
                    // Match {#each array as item} or {#each array as item, index} ... {/each}
                    const eachRegex = /\{#each\s+(\w+)\s+as\s+(\w+)(?:,\s*(\w+))?\}([\s\S]*?)\{\/each\}/g;
                    const match = eachRegex.exec(content);

                    if (match) {
                        const [fullMatch, arrayName, itemName, indexName, template] = match;
                        
                        // Create placeholder comment
                        const placeholder = document.createComment('each-loop');
                        parent.innerHTML = parent.innerHTML.replace(fullMatch, '');
                        parent.appendChild(placeholder);
                        
                        // Setup reactive loop
                        effect(() => {
                            // Clear previous items
                            let next = placeholder.nextSibling;
                            while (next && next.nodeType !== Node.COMMENT_NODE) {
                                const toRemove = next;
                                next = next.nextSibling;
                                toRemove.remove();
                            }
                            
                            // Render new items
                            if (state[arrayName]) {
                                const items = state[arrayName].value;
                                items.forEach((item, index) => {
                                    let itemHtml = template.replace(new RegExp(`\\{${itemName}\\}`, 'g'), item);

                                    // Replace index placeholder if indexName is provided
                                    if (indexName) {
                                        itemHtml = itemHtml.replace(new RegExp(`\\{${indexName}\\}`, 'g'), index);
                                    }

                                    const tempDiv = document.createElement('div');
                                    tempDiv.innerHTML = itemHtml.trim();

                                    // Insert each child after placeholder
                                    Array.from(tempDiv.children).forEach(child => {
                                        placeholder.parentNode.insertBefore(child, placeholder.nextSibling);
                                    });
                                });
                            }
                        });
                    }
                });
            };
            
            // Process each loops
            processEachLoops(content);
            
            // Process {#if} conditionals
            const processConditionals = (element) => {
                const walker = document.createTreeWalker(
                    element,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    if (node.textContent.includes('{#if')) {
                        textNodes.push(node);
                    }
                }
                
                textNodes.forEach(textNode => {
                    const parent = textNode.parentElement;
                    const content = parent.innerHTML;
                    
                    // Match {#if condition} ... {#else if condition} ... {#else} ... {/if}
                    const ifRegex = /\{#if\s+(\w+)\}([\s\S]*?)(?:\{#else\s+if\s+(\w+)\}([\s\S]*?))?(?:\{#else\}([\s\S]*?))?\{\/if\}/g;
                    const match = ifRegex.exec(content);
                    
                    if (match) {
                        const [fullMatch, condition1, template1, condition2, template2, elseTemplate] = match;
                        
                        // Create placeholder comment
                        const placeholder = document.createComment('if-block');
                        parent.innerHTML = parent.innerHTML.replace(fullMatch, '');
                        parent.appendChild(placeholder);
                        
                        // Setup reactive conditional
                        effect(() => {
                            // Clear previous content
                            let next = placeholder.nextSibling;
                            while (next && next.nodeType !== Node.COMMENT_NODE) {
                                const toRemove = next;
                                next = next.nextSibling;
                                toRemove.remove();
                            }
                            
                            // Determine which template to render
                            let templateToRender = '';
                            if (state[condition1] && state[condition1].value) {
                                templateToRender = template1;
                            } else if (condition2 && state[condition2] && state[condition2].value) {
                                templateToRender = template2;
                            } else if (elseTemplate) {
                                templateToRender = elseTemplate;
                            }
                            
                            // Render selected template
                            if (templateToRender) {
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = templateToRender.trim();
                                
                                // Insert each child after placeholder
                                Array.from(tempDiv.children).forEach(child => {
                                    placeholder.parentNode.insertBefore(child, placeholder.nextSibling);
                                });
                            }
                        });
                    }
                });
            };
            
            // Process conditionals
            processConditionals(content);
            
            // Process {@html} directives
            const processHtmlDirectives = (element) => {
                const walker = document.createTreeWalker(
                    element,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    if (node.textContent.includes('{@html')) {
                        textNodes.push(node);
                    }
                }
                
                textNodes.forEach(textNode => {
                    const parent = textNode.parentElement;
                    const content = parent.innerHTML;
                    
                    // Match {@html variable}
                    const htmlRegex = /\{@html\s+(\w+)\}/g;
                    const match = htmlRegex.exec(content);
                    
                    if (match) {
                        const [fullMatch, variableName] = match;
                        
                        // Create placeholder comment
                        const placeholder = document.createComment('html-block');
                        parent.innerHTML = parent.innerHTML.replace(fullMatch, '');
                        parent.appendChild(placeholder);
                        
                        // Setup reactive HTML injection
                        effect(() => {
                            // Clear previous content
                            let next = placeholder.nextSibling;
                            while (next && next.nodeType !== Node.COMMENT_NODE) {
                                const toRemove = next;
                                next = next.nextSibling;
                                toRemove.remove();
                            }
                            
                            // Inject HTML content
                            if (state[variableName]) {
                                const htmlString = state[variableName].value;
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = htmlString;
                                
                                // Insert each child after placeholder
                                Array.from(tempDiv.children).forEach(child => {
                                    placeholder.parentNode.insertBefore(child, placeholder.nextSibling);
                                });
                            }
                        });
                    }
                });
            };
            
            // Process HTML directives
            processHtmlDirectives(content);
            
            // Setup methods with proper context
            methods = config.methods || {};
            const context = new Proxy({}, {
                get(target, prop) {
                    if (state[prop]) return state[prop].value;
                    return target[prop];
                },
                set(target, prop, value) {
                    if (state[prop]) {
                        state[prop].value = value;
                        return true;
                    }
                    target[prop] = value;
                    return true;
                }
            });
            
            // Bind methods globally
            Object.keys(methods).forEach(key => {
                window[key] = function() {
                    methods[key].call(context);
                };
            });

            // Execute effects if provided
            if (config.effects && typeof config.effects === 'function') {
                config.effects.call(context);
            }

            // Setup reactive text nodes and standalone expressions
            const walker = document.createTreeWalker(
                content,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                if (node.textContent.includes('{') && 
                    !node.textContent.includes('{#each') && 
                    !node.textContent.includes('{#if') &&
                    !node.textContent.includes('{@html')) {
                    textNodes.push(node);
                }
            }
            
            // Also check for standalone expressions in template root
            const rootWalker = document.createTreeWalker(
                content,
                NodeFilter.SHOW_ALL,
                null,
                false
            );
            
            const standaloneNodes = [];
            let rootNode;
            while (rootNode = rootWalker.nextNode()) {
                if (rootNode.nodeType === Node.TEXT_NODE && 
                    rootNode.textContent.trim().match(/^\{[^}]+\}$/)) {
                    standaloneNodes.push(rootNode);
                }
            }
            
            standaloneNodes.forEach(rootNode => {
                // Standalone expression - wrap in span
                if (rootNode.parentNode) {
                    const span = document.createElement('span');
                    const expression = rootNode.textContent.trim();
                    
                    // Replace node first
                    rootNode.parentNode.replaceChild(span, rootNode);
                    
                    // Setup reactive expression
                    effect(() => {
                        const expr = expression.slice(1, -1).trim();
                        try {
                            const contextVars = {};
                            Object.keys(state).forEach(key => {
                                contextVars[key] = state[key].value;
                            });
                            
                            const func = new Function(...Object.keys(contextVars), `return ${expr}`);
                            const value = func(...Object.values(contextVars));
                            span.textContent = value;
                        } catch (e) {
                            span.textContent = expression;
                        }
                    });
                }
            });
            
            textNodes.forEach(textNode => {
                const template = textNode.textContent;
                const matches = template.match(/\{([^}]+)\}/g);
                
                if (matches) {
                    effect(() => {
                        let result = template;
                        matches.forEach(match => {
                            const expression = match.slice(1, -1).trim();
                            
                            try {
                                // Create context with state variables
                                const contextVars = {};
                                Object.keys(state).forEach(key => {
                                    contextVars[key] = state[key].value;
                                });
                                
                                // Execute JavaScript expression with context
                                const func = new Function(...Object.keys(contextVars), `return ${expression}`);
                                const value = func(...Object.values(contextVars));
                                result = result.replace(match, value);
                            } catch (e) {
                                // Fallback to simple variable lookup
                                const key = expression;
                                if (state[key]) {
                                    result = result.replace(match, state[key].value);
                                } else {
                                    result = result.replace(match, match);
                                }
                            }
                        });
                        
                        // If parent is body or no parent tag, wrap in span
                        const parent = textNode.parentElement;
                        if (parent && textNode.parentNode && 
                            (!parent || parent.tagName === 'BODY' || parent === mountTarget)) {
                            if (textNode.nodeType === Node.TEXT_NODE && textNode.textContent.trim() === template.trim()) {
                                // Replace text node with span
                                const span = document.createElement('span');
                                span.textContent = result;
                                textNode.parentNode.replaceChild(span, textNode);
                                return;
                            }
                        }
                        
                        if (textNode.parentNode) {
                            textNode.textContent = result;
                        }
                    });
                }
            });
            
            // Mount to DOM
            mountTarget.appendChild(content);
        }
    };
}

export { createApp };